package com.example.stopky.ui.decay

import android.app.DatePickerDialog
import android.content.Context
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.fragment.app.Fragment
import androidx.preference.PreferenceManager
import com.example.stopky.R
import java.text.SimpleDateFormat
import java.util.*
import kotlin.math.exp
import kotlin.math.ln

class DecayFragment : Fragment() {

    private lateinit var spinnerIsotope: Spinner
    private lateinit var inputActivity: EditText
    private lateinit var inputStartDate: EditText
    private lateinit var inputTargetDate: EditText
    private lateinit var textCurrentActivity: TextView
    private lateinit var textTargetActivity: TextView

    private val isotopes = mapOf(
        "Ir192" to 73.83,   // poločas v dnech
        "Se75" to 119.77,
        "Co60" to 1925.0
    )

    private val dateFormat = SimpleDateFormat("dd.MM.yyyy", Locale.getDefault())

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_decay, container, false)
        spinnerIsotope = view.findViewById(R.id.spinnerIsotope)
        inputActivity = view.findViewById(R.id.inputActivity)
        inputStartDate = view.findViewById(R.id.inputStartDate)
        inputTargetDate = view.findViewById(R.id.inputTargetDate)
        textCurrentActivity = view.findViewById(R.id.textCurrentActivity)
        textTargetActivity = view.findViewById(R.id.textTargetActivity)

        // Spinner setup
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, isotopes.keys.toList())
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerIsotope.adapter = adapter

        // Date pickers
        inputStartDate.setOnClickListener { showDatePicker(inputStartDate) }
        inputTargetDate.setOnClickListener { showDatePicker(inputTargetDate) }

        // Načtení uložených hodnot
        val prefs = requireContext().getSharedPreferences("decay_prefs", Context.MODE_PRIVATE)
        spinnerIsotope.setSelection(isotopes.keys.indexOf(prefs.getString("isotope", "Ir192")))
        inputActivity.setText(prefs.getString("activity", ""))
        inputStartDate.setText(prefs.getString("startDate", ""))

        // Načtení cílového data (bez automatického vymazávání)
        inputTargetDate.setText(prefs.getString("targetDate", ""))

        // Watchers
        val watcher = object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                saveInputs()
                calculateActivities()
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        }
        inputActivity.addTextChangedListener(watcher)
        inputStartDate.addTextChangedListener(watcher)
        inputTargetDate.addTextChangedListener(watcher)
        spinnerIsotope.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>, view: View?, position: Int, id: Long) {
                saveInputs()
                calculateActivities()
            }
            override fun onNothingSelected(parent: AdapterView<*>) {}
        }

        // Výpočet při otevření, pokud jsou data
        calculateActivities()

        return view
    }

    private fun showDatePicker(target: EditText) {
        val calendar = Calendar.getInstance()

        // Pro cílové datum nastavíme výchozí datum o měsíc dopředu
        if (target == inputTargetDate) {
            calendar.add(Calendar.MONTH, 1)
        }

        val dialog = DatePickerDialog(requireContext(),
            { _, year, month, dayOfMonth ->
                calendar.set(year, month, dayOfMonth)
                target.setText(dateFormat.format(calendar.time))
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )
        dialog.show()
    }

    private fun saveInputs() {
        val prefs = requireContext().getSharedPreferences("decay_prefs", Context.MODE_PRIVATE)
        prefs.edit()
            .putString("isotope", spinnerIsotope.selectedItem.toString())
            .putString("activity", inputActivity.text.toString())
            .putString("startDate", inputStartDate.text.toString())
            .putString("targetDate", inputTargetDate.text.toString())
            .apply()
    }

    private fun calculateActivities() {
        val isotope = spinnerIsotope.selectedItem.toString()
        val halfLife = isotopes[isotope] ?: return
        val activity0 = inputActivity.text.toString().replace(',', '.').toDoubleOrNull()
        val startDateStr = inputStartDate.text.toString()
        val targetDateStr = inputTargetDate.text.toString()
        val today = Calendar.getInstance().time

        // Výpočet aktuální aktivity (od počátečního data do dneška)
        val currentActivity = if (activity0 != null && startDateStr.isNotEmpty()) {
            val startDate = dateFormat.parse(startDateStr)
            if (startDate != null) {
                val days = ((today.time - startDate.time) / (1000 * 60 * 60 * 24)).toDouble()
                android.util.Log.d("DecayCalc", "Current: Start=$startDateStr, Today=${dateFormat.format(today)}, Days=$days")
                decay(activity0, halfLife, days)
            } else null
        } else null

        textCurrentActivity.text = if (currentActivity != null) String.format("%.1f", currentActivity) else "0.0"

        // Výpočet aktivity v cílovém datu (od počátečního data do cílového data)
        val targetActivity = if (activity0 != null && startDateStr.isNotEmpty() && targetDateStr.isNotEmpty()) {
            val startDate = dateFormat.parse(startDateStr)
            val targetDate = dateFormat.parse(targetDateStr)
            if (startDate != null && targetDate != null) {
                val days = ((targetDate.time - startDate.time) / (1000 * 60 * 60 * 24)).toDouble()
                android.util.Log.d("DecayCalc", "Target: Start=$startDateStr, Target=$targetDateStr, Days=$days")
                decay(activity0, halfLife, days)
            } else null
        } else null

        textTargetActivity.text = if (targetActivity != null) String.format("%.1f", targetActivity) else "0.0"

        // Kontrola, zda jsou data stejná
        if (startDateStr.isNotEmpty() && targetDateStr.isNotEmpty() && startDateStr == targetDateStr) {
            textTargetActivity.text = "⚠️ Stejné datum jako počáteční!"
        }

        // Debug log pro porovnání výsledků
        android.util.Log.d("DecayCalc", "Results: Current=${textCurrentActivity.text}, Target=${textTargetActivity.text}")
    }

    private fun decay(a0: Double, halfLife: Double, days: Double): Double {
        val lambda = ln(2.0) / halfLife
        return a0 * exp(-lambda * days)
    }
}