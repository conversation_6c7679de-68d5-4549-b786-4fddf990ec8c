<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    <!-- Vstupní parametry Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="14dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#424242">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Typ zářiče -->
            <TextView
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="@string/label_isotope_type"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                style="@style/Text.Info"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <Spinner
                android:id="@+id/spinnerIsotope"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginBottom="12dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Počateční aktivita -->
            <TextView
                android:id="@+id/labelInitialActivity"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="@string/label_initial_activity"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                style="@style/Text.Info"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <EditText
                android:id="@+id/inputActivity"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginBottom="12dp"
                android:inputType="numberDecimal"
                android:textSize="14sp"
                android:gravity="center"
                android:hint="@string/iqi_thickness_hint"
                android:textColor="@android:color/white"
                style="@style/Text.Input"
                android:padding="8dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Počáteční datum -->
            <TextView
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="@string/label_start_date"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                style="@style/Text.Info"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <EditText
                android:id="@+id/inputStartDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:focusable="false"
                android:clickable="true"
                android:hint="@string/hint_select_date"
                android:layout_marginBottom="12dp"
                android:inputType="numberDecimal"
                android:textSize="14sp"
                android:gravity="center"
                android:textColor="@android:color/white"
                style="@style/Text.Input"
                android:padding="8dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Cílové datum -->
            <TextView
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="@string/label_target_date"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                style="@style/Text.Info"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <EditText
                android:id="@+id/inputTargetDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:focusable="false"
                android:clickable="true"
                android:hint="@string/hint_select_date"
                android:layout_marginBottom="12dp"
                android:inputType="numberDecimal"
                android:textSize="14sp"
                android:gravity="center"
                android:textColor="@android:color/white"
                style="@style/Text.Input"
                android:padding="8dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Dnešní aktivita -->
            <TextView
                android:id="@+id/labelCurrentActivity"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="@string/label_current_activity"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                style="@style/Text.Info"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/textCurrentActivity"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="0.00"
                android:layout_marginBottom="12dp"
                android:gravity="center_horizontal"
                style="@style/Text.Result"
                android:padding="8dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Actita v cílovém datu -->
            <TextView
                android:id="@+id/labelTargetActivity"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:text="@string/label_target_activity"
                android:textSize="12sp"
                android:textColor="@android:color/white"
                style="@style/Text.Info"
                android:layout_marginEnd="8dp"
                android:gravity="center_vertical" />

            <TextView
                android:id="@+id/textTargetActivity"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="0.00"
                android:layout_marginBottom="12dp"
                android:gravity="center_horizontal"
                style="@style/Text.Result"
                android:padding="8dp" />
        </LinearLayout>

    </LinearLayout>
    </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>