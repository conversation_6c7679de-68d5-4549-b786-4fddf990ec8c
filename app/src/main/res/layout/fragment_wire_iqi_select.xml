<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Vstupní parametry Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="14dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#424242">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="5dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="4dp">

                <!-- Materiál objektu -->
                <TextView
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_material_label"
                    android:textSize="14sp"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="5dp" />

                <Spinner
                    android:id="@+id/spinnerMaterial"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="5dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <Space
                    android:layout_width="0sp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="5dp" />

                <!-- Tloušťka -->
                <TextView
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_thickness_label"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp"
                    android:gravity="center_vertical" />

                <EditText
                    android:id="@+id/editTextThickness"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp"
                    android:inputType="numberDecimal"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:hint="@string/iqi_thickness_hint"
                    android:textColor="@android:color/white"
                    android:padding="8dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Třída zkoušky -->
                <TextView
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_testing_class_label"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp"
                    android:gravity="center_vertical" />

                <Spinner
                    android:id="@+id/spinnerTestingClass"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Typ měrky -->
                <TextView
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_type_label"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp"
                    android:gravity="center_vertical" />

                <Spinner
                    android:id="@+id/spinnerIqiType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Norma výběru IQI -->
                <TextView
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_standard_label"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp"
                    android:gravity="center_vertical" />

                <Spinner
                    android:id="@+id/spinnerStandard"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Technika snímkování -->
                <TextView
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_technique_label"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp"
                    android:gravity="center_vertical" />

                <Spinner
                    android:id="@+id/spinnerTechnique"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp">

                <!-- Umístění měrky -->
                <TextView
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_placement_label"
                    android:textSize="12sp"
                    android:textColor="@android:color/white"
                    android:layout_marginEnd="8dp"
                    android:gravity="center_vertical" />

                <Spinner
                    android:id="@+id/spinnerPlacement"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginBottom="12dp" />
            </LinearLayout>

    </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Výsledek Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardResult"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="#424242">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="8dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/iqi_result_title"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="12dp" />

                <!-- Požadovaná IQI měrka -->
                <TextView
                    android:id="@+id/textViewResult"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <!-- Materiál měrky -->
                <TextView
                    android:id="@+id/textViewMaterial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <!-- Doporučená sada měrek -->
                <TextView
                    android:id="@+id/textViewRecommendedSet"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="8dp" />

                <!-- Varování pro F měrky -->
                <TextView
                    android:id="@+id/textViewWarning"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textColor="#FF9800"
                    android:visibility="gone"
                    android:layout_marginTop="8dp" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>