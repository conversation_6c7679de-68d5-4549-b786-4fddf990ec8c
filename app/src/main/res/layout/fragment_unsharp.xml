<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <!-- Vstupní parametry Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="14dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="#424242">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="5dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp">

        <!-- Velikost ohniska -->
        <TextView
            android:layout_width="220dp"
            android:layout_height="wrap_content"
            android:text="@string/label_focal_spot_size"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_marginBottom="5dp" />

        <EditText
            android:id="@+id/inputF"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginBottom="12dp"
            android:inputType="numberDecimal"
            android:textSize="14sp"
            android:gravity="center"
            android:hint="@string/iqi_thickness_hint"
            android:textColor="@android:color/white"
            android:padding="8dp" />

    </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Vzdalenost zdroj-objekt -->
            <TextView
                android:layout_width="220dp"
                android:layout_height="wrap_content"
                android:text="@string/label_distance_source_subject"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:layout_marginBottom="5dp" />

            <EditText
                android:id="@+id/inputA"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginBottom="12dp"
                android:inputType="numberDecimal"
                android:textSize="14sp"
                android:gravity="center"
                android:hint="@string/iqi_thickness_hint"
                android:textColor="@android:color/white"
                android:padding="8dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Vzdalenost zdroj-objekt -->
            <TextView
                android:layout_width="220dp"
                android:layout_height="wrap_content"
                android:text="@string/label_distance_subject_film"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:layout_marginBottom="5dp" />

            <EditText
                android:id="@+id/inputB"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginBottom="12dp"
                android:inputType="numberDecimal"
                android:textSize="14sp"
                android:gravity="center"
                android:hint="@string/iqi_thickness_hint"
                android:textColor="@android:color/white"
                android:padding="8dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Výsledek -->
            <TextView
                android:layout_width="220dp"
                android:layout_height="wrap_content"
                android:text="@string/label_geometric_unsharpness"
                android:textSize="14sp"
                android:textColor="@android:color/white"
                android:layout_marginBottom="5dp" />

            <TextView
                android:id="@+id/textResult"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="0.00"
                android:textSize="30sp"
                android:textStyle="bold"
                android:gravity="center_horizontal"
                style="@style/Text.Result"
                android:layout_marginBottom="12dp"
                android:padding="8dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp">

            <!-- Obrázek schématu -->
            <ImageView
                android:id="@+id/imageScheme"
                android:layout_width="match_parent"
                android:layout_height="340dp"
                android:src="@drawable/unsharp_scheme"
                android:scaleType="fitCenter"
                android:contentDescription="@string/unsharp_scheme_desc"
                android:layout_marginTop="12dp" />
        </LinearLayout>

    </LinearLayout>

    </androidx.cardview.widget.CardView>

    </LinearLayout>
</ScrollView>